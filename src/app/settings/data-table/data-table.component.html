@if (search) {
   <div class="center-flex">
      <mat-form-field style="width: 40vw;">
         <mat-label>Търсене</mat-label>
         <mat-icon matSuffix>search</mat-icon>
         <input [formControl]="filter" autocomplete="off" matInput type="text">
      </mat-form-field>
   </div>
}
<div class="mat-elevation-z2">
   <table [dataSource]="dataSource" mat-table multiTemplateDataRows>

      <ng-container matColumnDef="dataTableSelect">
         <th *matHeaderCellDef mat-header-cell>
            <mat-checkbox (change)="$event ? masterToggle() : null"
                          [checked]="selection.hasValue() && isAllSelected()"
                          [indeterminate]="selection.hasValue() && !isAllSelected()"
                          color="primary"/>
         </th>
         <td *matCellDef="let row" mat-cell>
            <mat-checkbox (change)="$event ? selection.toggle(row) : null"
                          [checked]="selection.isSelected(row)"
                          color="primary"/>
         </td>
      </ng-container>

      <ng-container matColumnDef="dataTableActions">
         <th *matHeaderCellDef [attr.colspan]="headerColumns.length - 1" mat-header-cell>
            @if (selection.hasValue()) {
               <div [@inOut] class="flex-row btn-row">
                  <ng-content select="[selectionActions]"/>
                  <button color="warn" mat-raised-button (click)="deleteSelected()">
                     <mat-icon>delete</mat-icon>
                     Изтрий
                  </button>
               </div>
            }
         </th>
      </ng-container>

      <ng-container matColumnDef="dataTableEmpty">
         <th *matHeaderCellDef mat-header-cell></th>
      </ng-container>

      @for (header of headers; track header) {
         <ng-container [matColumnDef]="header">
            <th mat-header-cell *matHeaderCellDef><i>{{header}}</i></th>
            <td mat-cell *matCellDef="let row">
               @for (value of row | property: properties[$index]; track value) {
                  <div>{{value}}</div>
               }
            </td>
         </ng-container>
      }

      <ng-container matColumnDef="dataTableExpand">
         <th *matHeaderCellDef mat-header-cell [style.width]="customActions?.length ? '180px' : '130px'">&nbsp;</th>
         <td *matCellDef="let row" mat-cell>
            @if (expanded === row) {
               <div style="display: flex; flex-direction: row;">
                  <button (click)="shrink(false)" mat-icon-button color="warn">
                     <mat-icon>close</mat-icon>
                  </button>
                  <button (click)="shrink(true)" mat-icon-button color="primary">
                     <mat-icon>check</mat-icon>
                  </button>
               </div>
            } @else {
               <div style="display: flex; flex-direction: row;">
                  <button (click)="expand(row)" mat-icon-button>
                     <mat-icon>edit</mat-icon>
                  </button>
                  @if (customActions) {
                     @for (action of customActions; track action.icon) {
                        @if (!action.condition || action.condition(row)) {
                           <button (click)="action.action(row)" mat-icon-button
                                   [matTooltip]="action.tooltip" matTooltipPosition="above">
                              <mat-icon>{{action.icon}}</mat-icon>
                           </button>
                        }
                     }
                  }
               </div>
            }
         </td>
      </ng-container>

      <ng-container matColumnDef="dataTableEdit">
         <td *matCellDef="let row" [attr.colspan]="headerColumns.length" mat-cell>
            <div [@detailExpand]="row === expanded ? 'expanded' : 'collapsed'">
               @if (row === expanded) {
                  <ng-container
                     *ngComponentOutlet="formClass; inputs: {data: row, edit: true}"/>
               } @else {
                  <div style="height: 200px"></div>
               }
            </div>
         </td>
      </ng-container>

      <!-- First header row-->
      <tr *matHeaderRowDef="['dataTableSelect', 'dataTableActions']"
          class="sticky-row r-1" mat-header-row></tr>

      <!-- Second header row-->
      <tr *matHeaderRowDef="headerColumns" class="sticky-row r-2" mat-header-row></tr>

      <tr *matRowDef="let row; columns: tableColumns;" mat-row style="height: 2em"></tr>
      <tr *matRowDef="let row; columns: ['dataTableEdit']" class="form-row" mat-row></tr>

      <tr *matNoDataRow>
         <td [colSpan]="tableColumns.length" class="text-center">
            Няма елементи за визуализация
            @if (filter?.value) {
               <p (click)="filter.setValue(null)" class="primary-text clickable">
                  Премахни филтъра "{{filter.value}}"
               </p>
            }
         </td>
      </tr>
   </table>
   @if (!noPaginator) {
      <mat-paginator [pageSizeOptions]="[20, 50, 100]" showFirstLastButtons/>
   }
</div>
