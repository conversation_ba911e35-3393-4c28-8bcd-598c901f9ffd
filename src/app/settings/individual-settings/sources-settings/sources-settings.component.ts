import {
   AfterViewInit,
   Component,
   inject,
   ViewChild
} from '@angular/core';
import {ReservationSourceService} from '../../../services/reservation-source.service';
import {
   ReservationSource,
   ReservationSourceCode,
   ReservationSourceLables
} from '../../../data/reservation-source';
import {cmpName} from '../../../utility/utility';
import {CustomAction, DataTableService, DataType} from '../../data-table/data-table.service';
import {DataTableComponent} from '../../data-table/data-table.component';
import {ReservationSourceFormComponent} from '../../../forms/reservation-source-form/reservation-source-form.component';
import {MatDialog} from '@angular/material/dialog';
import {FinancialAccountService} from '../../../services/financial-account.service';
import {BalanceDialogComponent, BalanceDialogData, FinancialContext} from '../../../dialogs/balance-dialog/balance-dialog.component';
import {fullScreenDialogOptions} from '../../../utility/dialog-utility';
import {FinancialAccountPipe} from '../../../pipes/financial-account.pipe';
import {switchMap} from 'rxjs/operators';

@Component({
   selector: 'app-sources-settings',
   templateUrl: './sources-settings.component.html',
   standalone: false
})
export class SourcesSettingsComponent implements AfterViewInit {
   @ViewChild(DataTableComponent) dataTable!: DataTableComponent;

   sources: ReservationSource[] = [];

   DataType = DataType;
   ReservationSourceFormComponent = ReservationSourceFormComponent;

   headers = ['Име', 'Код', 'Канал', 'Изисква идентификатор', 'По подразбиране'];
   properties = [
      'name',
      'code',
      (source: ReservationSource) => source.channelCode ? ReservationSourceLables[source.channelCode] : '-',
      (source: ReservationSource) => source.requireIdentifier ? 'Да' : 'Не',
      (source: ReservationSource) => source.isDefault ? 'Да' : 'Не'
   ];

   sourceCodes = Object.values(ReservationSourceCode).map(value => ({
      value,
      label: ReservationSourceLables[value]
   }));

   customActions: CustomAction[] = [
      {
         icon: 'attach_money',
         tooltip: 'Отвори сметката',
         action: (source: ReservationSource) => this.openFinancialAccount(source),
         condition: (source: ReservationSource) => !!source.financialAccount
      }
   ];

   private sReservationSource = inject(ReservationSourceService);
   private sDataTable = inject(DataTableService);
   private sFinancialAccount = inject(FinancialAccountService);
   private dialog = inject(MatDialog);

   constructor() {
      this.sDataTable.registerHandlers(
         ({type, data}) => type === DataType.ReservationSource &&
            this.sReservationSource.update(data)
               .subscribe(() => this.renewReservationSources()),
         () => {throw Error("Unsupported operation")}
      );
   }

   ngAfterViewInit(): void {
      this.renewData();
   }

   private renewData(): void {
      this.renewReservationSources();
   }

   addSource(): void {
      this.dataTable.expand({});
   }

   openFinancialAccount(source: ReservationSource): void {
      if (!source.financialAccount) {
         return;
      }

      this.sFinancialAccount.get(source.financialAccount).pipe(
         switchMap(account => {
            const accountChain = {
               id: account.titular.id,
               activeAccount: account,
               accounts: [account]
            };

            const data: BalanceDialogData = {
               accountChain,
               movementAllowed: true,
               context: FinancialContext.customer,
               subtitle: FinancialAccountPipe.toString(account, true)
            };

            return this.dialog.open(BalanceDialogComponent, {
               data,
               ...fullScreenDialogOptions
            }).afterClosed();
         })
      ).subscribe();
   }

   private renewReservationSources(): void {
      this.sReservationSource.getAll().subscribe(sources => {
         const receptionFirst = (fa1: ReservationSource, fa2: ReservationSource) =>
            // @ts-expect-error You CAN subtract booleans
            !fa2.financialAccount - !fa1.financialAccount;
         this.sources = sources.sort(cmpName).sort(receptionFirst);
      });
   }
}
