import {
   AfterViewInit,
   Component,
   inject,
   ViewChild
} from '@angular/core';
import {ReservationSourceService} from '../../../services/reservation-source.service';
import {
   ReservationSource,
   ReservationSourceCode,
   ReservationSourceLables
} from '../../../data/reservation-source';
import {cmpName} from '../../../utility/utility';
import {DataTableService, DataType} from '../../data-table/data-table.service';
import {DataTableComponent} from '../../data-table/data-table.component';

@Component({
   selector: 'app-sources-settings',
   templateUrl: './sources-settings.component.html',
   standalone: false
})
export class SourcesSettingsComponent implements AfterViewInit {
   @ViewChild(DataTableComponent) dataTable!: DataTableComponent;

   sources: ReservationSource[] = [];

   DataType = DataType;
   sourceCodes = Object.values(ReservationSourceCode).map(value => ({
      value,
      label: ReservationSourceLables[value]
   }));

   private sReservationSource = inject(ReservationSourceService);
   private sDataTable = inject(DataTableService);

   constructor() {
      this.sDataTable.registerHandlers(
         ({type, data}) => type === DataType.ReservationSource &&
            this.sReservationSource.update(data)
               .subscribe(() => this.renewReservationSources()),
         () => {throw Error("Unsupported operation")}
      );
   }

   ngAfterViewInit(): void {
      this.renewData();
   }

   private renewData(): void {
      this.renewReservationSources();
   }

   private renewReservationSources(): void {
      this.sReservationSource.getAll().subscribe(sources => {
         const receptionFirst = (fa1: ReservationSource, fa2: ReservationSource) =>
            // @ts-expect-error You CAN subtract booleans
            !fa2.financialAccount - !fa1.financialAccount;
         this.sources = sources.sort(cmpName).sort(receptionFirst);
      });
   }
}
