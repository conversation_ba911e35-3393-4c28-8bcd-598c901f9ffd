import {Component, inject, Input, OnInit} from '@angular/core';
import {UntypedFormBuilder, Validators} from '@angular/forms';
import {ReservationSource, ReservationSourceCode, ReservationSourceLables} from '../../data/reservation-source';
import {DataTableService} from '../../settings/data-table/data-table.service';
import {FinancialAccountService} from '../../services/financial-account.service';
import {FinancialAccount} from '../../data/financial-account';
import {equalIdentifiables} from '../../utility/utility';

@Component({
  selector: 'app-reservation-source-form',
  templateUrl: './reservation-source-form.component.html',
  standalone: false
})
export class ReservationSourceFormComponent implements OnInit {
  @Input() data?: ReservationSource;
  @Input() edit = false;

  form = inject(UntypedFormBuilder).group({
    id: '',
    name: ['', Validators.required],
    code: '',
    financialAccount: '',
    channelCode: '',
    requireIdentifier: [false]
  });

  financialAccounts: FinancialAccount[] = [];
  sourceCodes = Object.values(ReservationSourceCode).map(value => ({
    value,
    label: ReservationSourceLables[value]
  }));
  
  equalFinancialAccounts = equalIdentifiables;

  private sDataTable = inject(DataTableService);
  private sFinancialAccount = inject(FinancialAccountService);

  get valid(): boolean {
    return this.form.valid;
  }

  get value(): ReservationSource {
    return this.form.value;
  }

  ngOnInit(): void {
    if (this.data) {
      this.form.patchValue(this.data);
    }

    this.sFinancialAccount.getAll().subscribe(accounts => {
      this.financialAccounts = accounts;
    });

    this.form.valueChanges.subscribe(v => this.sDataTable.result = v);
  }
}
